<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zsmall.system.entity.mapper.AnnouncementMapper">

    <!-- 分页查询公告列表（通用接口） -->
    <select id="queryPageList" resultType="com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo">
        SELECT
            a.id,
            a.title,
            a.tenant_type,
            a.status,
            a.expired_time,
            a.is_expired,
            a.content,
            a.create_time,
            a.update_time
        FROM announcement a
        <if test="tenantId != null and tenantId != ''">
            <!-- 分销商/供应商查询：关联已读表，确保只查询属于该租户且未被删除的公告 -->
            INNER JOIN announcement_tenant ar ON a.id = ar.announcement_id AND ar.tenant_id = #{tenantId} AND ar.del_flag = 0
        </if>
        WHERE a.del_flag = 0
        <if test="bo.title != null and bo.title != ''">
            AND a.title LIKE CONCAT('%', #{bo.title}, '%')
        </if>
        <if test="bo.tenantType != null and bo.tenantType != ''">
            AND a.tenant_type = #{bo.tenantType}
        </if>
        <if test="bo.status != null">
            AND a.status = #{bo.status}
        </if>
        <if test="bo.isExpired != null">
            AND a.is_expired = #{bo.isExpired}
        </if>
        <if test="bo.expiredStartTime != null">
            AND a.expired_time &gt;= #{bo.expiredStartTime}
        </if>
        <if test="bo.expiredEndTime != null">
            AND a.expired_time &lt;= #{bo.expiredEndTime}
        </if>
        <if test="bo.createStartTime != null">
            AND a.create_time &gt;= #{bo.createStartTime}
        </if>
        <if test="bo.createEndTime != null">
            AND a.create_time &lt;= #{bo.createEndTime}
        </if>
        ORDER BY a.create_time DESC, a.id DESC
    </select>

    <!-- 自定义ResultMap处理公告和附件的一对多关系 -->
    <resultMap id="AnnouncementWithAttachmentsMap" type="com.zsmall.system.entity.domain.vo.announcement.AnnouncementVo">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="tenantType" column="tenant_type"/>
        <result property="status" column="status"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="isExpired" column="is_expired"/>
        <result property="content" column="content"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <!-- 一对多关系映射附件列表 -->
        <collection property="attachments" ofType="com.hengjian.system.domain.vo.SysOssVo">
            <id property="ossId" column="oss_id"/>
            <result property="businessNumber" column="business_number"/>
            <result property="businessId" column="business_id"/>
            <result property="fileName" column="file_name"/>
            <result property="originalName" column="original_name"/>
            <result property="fileSuffix" column="file_suffix"/>
            <result property="url" column="url"/>
            <result property="createTime" column="oss_create_time"/>
            <result property="createBy" column="oss_create_by"/>
            <result property="service" column="service"/>
        </collection>
    </resultMap>

    <!-- 查询公告详情（包含附件信息） -->
    <select id="selectAnnouncementWithAttachments" resultMap="AnnouncementWithAttachmentsMap">
        SELECT
        a.id,
        a.title,
        a.tenant_type,
        a.status,
        a.expired_time,
        a.is_expired,
        a.content,
        a.create_time,
        a.update_time,
        s.oss_id,
        s.business_number,
        s.business_id,
        s.file_name,
        s.original_name,
        s.file_suffix,
        s.url,
        s.create_time as oss_create_time,
        s.create_by as oss_create_by,
        s.service
        FROM announcement a
        left JOIN announcement_oss ao ON a.id = ao.announcement_id AND ao.del_flag = 0
        left JOIN sys_oss s ON ao.oss_id = s.oss_id
        WHERE a.id = #{id} AND a.del_flag = 0
        ORDER BY a.id desc
    </select>

    <select id="selectAnnouncementWithAttachmentList" resultMap="AnnouncementWithAttachmentsMap">
        SELECT
        a.id,
        a.title,
        a.tenant_type,
        a.status,
        a.expired_time,
        a.is_expired,
        a.content,
        a.create_time,
        a.update_time,
        s.oss_id,
        s.business_number,
        s.business_id,
        s.file_name,
        s.original_name,
        s.file_suffix,
        s.url,
        s.create_time as oss_create_time,
        s.create_by as oss_create_by,
        s.service
        FROM announcement a
        left JOIN announcement_oss ao ON a.id = ao.announcement_id AND ao.del_flag = 0
        left JOIN sys_oss s ON ao.oss_id = s.oss_id
        WHERE   a.del_flag = 0
            <if test="id != null and id.size() != 0">
                AND a.id IN
                <foreach collection="id" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        ORDER BY a.id desc
    </select>

    <!-- 查询用户端消息列表（分页） -->
    <select id="queryMessageList" resultType="com.zsmall.system.entity.domain.vo.announcement.AnnouncementMessageVo">
        SELECT
            a.id,
            a.title,
            COALESCE(ar.is_read, 0) as is_read,
            COALESCE(ar.is_show_window, 0) as is_show_window,
            a.create_time
        FROM announcement a
        LEFT JOIN announcement_tenant ar ON a.id = ar.announcement_id AND ar.tenant_id = #{tenantId}
        WHERE a.del_flag = 0
            AND a.status = 0
            AND a.is_expired = 0
            AND a.tenant_type = #{tenantType}
            AND a.expired_time > NOW()
            AND COALESCE(ar.del_flag, 0) = 0
        <if test="isRead != null">
            AND COALESCE(ar.is_read, 0) = #{isRead}
        </if>
        ORDER BY a.create_time DESC, a.id DESC
    </select>



    <!-- 查询有效的公告列表（未过期、已启用） -->
    <select id="queryValidAnnouncements" resultType="com.zsmall.system.entity.domain.Announcement">
        SELECT
            id,
            title,
            tenant_type,
            status,
            expired_time,
            is_expired,
            content,
            create_time,
            update_time,
            create_by,
            update_by
        FROM announcement
        WHERE del_flag = 0
            AND status = 0
            AND is_expired = 0
            AND tenant_type = #{tenantType}
            AND expired_time > NOW()
        ORDER BY create_time DESC
    </select>

    <select id="popWindowSearch" resultMap="AnnouncementWithAttachmentsMap">
        SELECT
        a.id,
        a.title,
        a.tenant_type,
        a.status,
        a.expired_time,
        a.is_expired,
        a.content,
        a.create_time,
        a.update_time,
        s.oss_id,
        s.business_number,
        s.business_id,
        s.file_name,
        s.original_name,
        s.file_suffix,
        s.url,
        s.create_time as oss_create_time,
        s.create_by as oss_create_by,
        s.service
        from announcement a
        inner join announcement_tenant ar on a.id = ar.announcement_id and ar.tenant_id = #{tenantId} and ar.del_flag = 0
        left join announcement_oss ao on a.id = ao.announcement_id and ao.del_flag = 0
        left join sys_oss s on ao.oss_id = s.oss_id
        where  a.del_flag = 0
        and ar.is_show_window = 0
        and a.status = 0
        and a.is_expired = 0
    </select>

    <update id="batchPopWindows">
        update announcement_tenant
        set is_show_window = 1
        where announcement_id in
        <foreach collection="announcementIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and tenant_id = #{tenantId}
    </update>

    <select id="countAnnonUnreadMessages" resultType="java.lang.Long">
        select count(1) from announcement_tenant where tenant_id = #{tenantId} and is_read = 0 and del_flag=0
    </select>

    <select id="countWarningSkuUnreadMessages" resultType="java.lang.Long">
        select count(1) from warning_message_tenant where tenant_id = #{tenantId} and is_read = 0 and del_flag=0
    </select>

    <delete id="deleteWithValidByIds">
        update announcement a
        left join announcement_oss ao on a.id = ao.announcement_id
        left join announcement_tenant at on a.id = at.announcement_id

        set a.del_flag=1 ,
        ao.del_flag=1,
        at.del_flag=1
        where a.id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>



</mapper>
