package com.zsmall.system.entity.domain.vo.announcement;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.system.domain.vo.SysOssVo;
import com.zsmall.system.entity.domain.Announcement;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 公告视图对象 announcement
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = Announcement.class)
public class AnnouncementVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 标题
     */
    @ExcelProperty(value = "标题")
    private String title;

    /**
     * 适用对象（租户类型）
     */
    @ExcelProperty(value = "适用对象")
    private String tenantType;

    /**
     * 状态 启用/禁用 0启用 1禁用
     */
    @ExcelProperty(value = "状态")
    private Integer status;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    private Date expiredTime;

    /**
     * 是否过期 0未过期 1已经过期
     */
    @ExcelProperty(value = "是否过期")
    private Integer isExpired;

    /**
     * 富文本内容
     */
    @ExcelProperty(value = "内容")
    private String content;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    private String createBy;

    /**
     * 附件列表（关联sys_oss表）
     */
    private List<SysOssVo> attachments;

}
